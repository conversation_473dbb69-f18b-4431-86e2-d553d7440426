<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Dashboard extends MY_Controller
{

    public function __construct()
    {
        parent::__construct();

        // Cargar modelos necesarios
        $this->load->model('Users_model');
        $this->load->model('Static_values_model');

        // Cargar librerías necesarias
        $this->load->library('template');
    }

    public function index(): void
    {
        if ($this->login_user->user_type === "staff") {
            $view_data = $this->prepare_staff_dashboard();
            $this->template->render("dashboard/index", $view_data);
        } else {
            $this->redirect_client_dashboard();
        }
    }

    public function save_sticky_note(): void
    {
        $sticky_note = $this->input->post("sticky_note", TRUE);
        $this->Users_model->save(["sticky_note" => $sticky_note], $this->login_user->id);
    }

    /**
     * Muestra la vista del dashboard para un proyecto específico.
     *
     * @param int $project_id ID del proyecto.
     */
    public function view(int $project_id = 0): void
    {
        $this->member_allowed($project_id);
        $this->session->set_userdata('project_context', $project_id);

        $view_data = $this->prepare_project_view_data($project_id);

        $this->template->render("dashboard/client_dashboard", $view_data);
    }

    /**
     * Prepara los datos para la vista del proyecto.
     *
     * @param int $project_id ID del proyecto.
     * @return array Datos de la vista.
     */
    private function prepare_project_view_data(int $project_id): array
    {
        $view_data = [];
        $view_data = array_merge($view_data, $this->load_project_data($project_id));
        $view_data = array_merge($view_data, $this->load_footprint_data($project_id));
        $view_data = array_merge($view_data, $this->load_unit_data($project_id, $view_data));
        $view_data = array_merge($view_data, $this->load_consumption_and_waste_data($project_id, $view_data));
        $view_data = array_merge($view_data, $this->load_settings_data($project_id, $view_data));
        $view_data = array_merge($view_data, $this->prepare_chart_data($view_data));
        $view_data = array_merge($view_data, $this->process_compromises_and_permits($project_id, $view_data));
        $view_data = array_merge($view_data, $this->load_calculation_data($project_id, $view_data));

        return $view_data;
    }

    /**
     * Carga los datos básicos del proyecto.
     *
     * @param int $id_proyecto ID del proyecto.
     * @return array Datos del proyecto.
     */
    private function load_project_data(int $id_proyecto): array
    {
        $project_info = $this->Projects_model->get_details(["id" => $id_proyecto])->row();

        return [
            'id_proyecto' => $id_proyecto,
            'rubro' => $this->Industries_model->get_one($project_info->id_industria)->nombre,
            'subrubro' => $this->Subindustries_model->get_one($project_info->id_tecnologia)->nombre,
            'proyecto' => $project_info,
            'client_id' => $this->login_user->client_id,
        ];
    }

    /**
     * Carga los datos de huella ambiental.
     *
     * @param int $id_proyecto ID del proyecto.
     * @return array Datos de huella.
     */
    private function load_footprint_data(int $id_proyecto): array
    {
        return [
            'huellas' => $this->get_project_footprints($id_proyecto, 1),
            'huellas_carbon' => $this->get_project_footprints($id_proyecto, 2),
            'huellas_water' => $this->get_project_footprints($id_proyecto, 3),
        ];
    }

    /**
     * Carga los datos de unidades y procesos.
     *
     * @param int $id_proyecto ID del proyecto.
     * @param array $view_data Datos de la vista.
     * @return array Datos de unidades.
     */
    private function load_unit_data(int $id_proyecto, array $view_data): array
    {
        $client_id = $view_data['client_id'];
        $project_info = $view_data['proyecto'];

        $unit_data = [
            'unidades_funcionales' => $this->Functional_units_model->get_details(["id_cliente" => $client_id, "id_proyecto" => $id_proyecto])->result(),
            'criterios_calculos' => $this->Unit_processes_model->get_rules_calculations_of_project($client_id, $project_info->id)->result(),
            'procesos_unitarios' => $this->Unit_processes_model->get_pu_of_projects($project_info->id)->result_array(),
            'id_unidad_volumen' => $this->get_report_unit($client_id, $id_proyecto, 2),
            'id_unidad_masa' => $this->get_report_unit($client_id, $id_proyecto, 1),
            'id_unidad_energia' => $this->get_report_unit($client_id, $id_proyecto, 4),
        ];

        $unit_data['unidad_volumen'] = $this->Unity_model->get_one($unit_data['id_unidad_volumen'])->nombre;
        $unit_data['unidad_masa'] = $this->Unity_model->get_one($unit_data['id_unidad_masa'])->nombre;
        $unit_data['unidad_energia'] = $this->Unity_model->get_one($unit_data['id_unidad_energia'])->nombre;

        $unit_data['unidad_volumen_nombre_real'] = $this->Unity_model->get_one($unit_data['id_unidad_volumen'])->nombre_real;
        $unit_data['unidad_masa_nombre_real'] = $this->Unity_model->get_one($unit_data['id_unidad_masa'])->nombre_real;
        $unit_data['unidad_energia_nombre_real'] = $this->Unity_model->get_one($unit_data['id_unidad_energia'])->nombre_real;

        return $unit_data;
    }

    /**
     * Carga los datos de consumo y residuo.
     *
     * @param int $id_proyecto ID del proyecto.
     * @param array $view_data Datos de la vista.
     * @return array Datos de consumo y residuo.
     */
    private function load_consumption_and_waste_data(int $id_proyecto, array $view_data): array
    {
        $client_id = $view_data['client_id'];

        return [
            'campos_unidad_consumo' => $this->Forms_model->get_details(["id_cliente" => $client_id, "id_proyecto" => $id_proyecto, "flujo" => "Consumo"])->result(),
            'campos_unidad_residuo' => $this->Forms_model->get_details(["id_cliente" => $client_id, "id_proyecto" => $id_proyecto, "flujo" => "Residuo"])->result(),
        ];
    }

    /**
     * Carga los datos de configuración.
     *
     * @param int $id_proyecto ID del proyecto.
     * @param array $view_data Datos de la vista.
     * @return array Datos de configuración.
     */
    private function load_settings_data(int $id_proyecto, array $view_data): array
    {
        $client_id = $view_data['client_id'];

        return [
            'environmental_footprints_settings' => $this->Client_environmental_footprints_settings_model->get_all_where(["id_cliente" => $client_id, "id_proyecto" => $id_proyecto, "deleted" => 0])->result(),
            'Client_consumptions_settings_model' => $this->Client_consumptions_settings_model,
            'Client_waste_settings_model' => $this->Client_waste_settings_model,
            'general_settings' => $this->General_settings_model->get_one_where(["id_proyecto" => $id_proyecto, "deleted" => 0]),
            'Categories_alias_model' => $this->Categories_alias_model,
            'Categories_model' => $this->Categories_model,
        ];
    }

    /**
     * Prepara los datos para los gráficos.
     *
     * @param array $view_data Datos de la vista.
     * @return array Datos para gráficos.
     */
    private function prepare_chart_data(array $view_data): array
    {
        $view_data["first_date_current_year"] = date('2023-01-01');
        $view_data["last_date_current_year"] = date('2023-12-31');
        $view_data['years'] = range(date('Y') - 2, date('Y'));
        $view_data['meses'] = ["Enero", "Febrero", "Marzo", "Abril", "Mayo", "Junio", "Julio", "Agosto", "Septiembre", "Octubre", "Noviembre", "Diciembre"];

        $view_data = array_merge($view_data, $this->calculate_consumption_data($view_data));
        $view_data = array_merge($view_data, $this->calculate_waste_data($view_data));
        $view_data = array_merge($view_data, $this->set_visibility_flags($view_data));

        return $view_data;
    }

    /**
     * Calcula los datos de consumo para los gráficos.
     *
     * @param array $view_data Datos de la vista.
     * @return array Datos de consumo.
     */
    private function calculate_consumption_data(array $view_data): array
    {
        $chart_data = $this->prepare_consumption_charts([
            'years' => $view_data['years'],
            'meses' => $view_data['meses'],
            'client_id' => $view_data['client_id'],
            'id_proyecto' => $view_data['id_proyecto'],
            'campos_unidad_consumo' => $view_data['campos_unidad_consumo'],
            'id_unidad_volumen' => $view_data['id_unidad_volumen'],
            'id_unidad_masa' => $view_data['id_unidad_masa'],
            'id_unidad_energia' => $view_data['id_unidad_energia'],
        ]);

        return [
            'array_id_materiales_valores_volumen' => $chart_data['array_id_materiales_valores_volumen'],
            'array_grafico_consumos_volumen_data' => $chart_data['array_grafico_consumos_volumen_data'],
            'array_id_materiales_valores_masa' => $chart_data['array_id_materiales_valores_masa'],
            'array_grafico_consumos_masa_data' => $chart_data['array_grafico_consumos_masa_data'],
            'array_id_materiales_valores_energia' => $chart_data['array_id_materiales_valores_energia'],
            'array_grafico_consumos_energia_data' => $chart_data['array_grafico_consumos_energia_data'],
        ];
    }

    /**
     * Calcula los datos de residuo para los gráficos.
     *
     * @param array $view_data Datos de la vista.
     * @return array Datos de residuo.
     */
    private function calculate_waste_data(array $view_data): array
    {
        $chart_data = $this->prepare_waste_charts([
            'years' => $view_data['years'],
            'meses' => $view_data['meses'],
            'client_id' => $view_data['client_id'],
            'id_proyecto' => $view_data['id_proyecto'],
            'campos_unidad_residuo' => $view_data['campos_unidad_residuo'],
            'id_unidad_volumen' => $view_data['id_unidad_volumen'],
            'id_unidad_masa' => $view_data['id_unidad_masa'],
        ]);

        return [
            'array_id_materiales_valores_volumen_residuo' => $chart_data['array_id_materiales_valores_volumen_residuo'],
            'array_grafico_residuos_volumen_data' => $chart_data['array_grafico_residuos_volumen_data'],
            'array_id_materiales_valores_masa_residuo' => $chart_data['array_id_materiales_valores_masa_residuo'],
            'array_grafico_residuos_masa_data' => $chart_data['array_grafico_residuos_masa_data'],
        ];
    }

    /**
     * Establece las banderas de visibilidad para tablas y gráficos.
     *
     * @param array $view_data Datos de la vista.
     * * @return array Datos de la vista con banderas de visibilidad.
     */
    private function set_visibility_flags(array $view_data): array
    {
        $client_id = $view_data['client_id'];
        $project_id = $view_data['id_proyecto'];

        $consumption_categories = $this->get_categories_by_unit_type($view_data['campos_unidad_consumo']);
        $waste_categories = $this->get_categories_by_unit_type($view_data['campos_unidad_residuo']);

        $client_consumption_settings = $this->Client_consumptions_settings_model->get_all_where([
            "id_cliente" => $client_id,
            "id_proyecto" => $project_id,
            "deleted" => 0
        ])->result_array();

        $client_waste_settings = $this->Client_waste_settings_model->get_all_where([
            "id_cliente" => $client_id,
            "id_proyecto" => $project_id,
            "deleted" => 0
        ])->result_array();

        return [
            "ocultar_tabla_consumos_volumen" => $this->should_hide_element($client_consumption_settings, $consumption_categories['volumen'], 'tabla'),
            "ocultar_grafico_consumos_volumen" => $this->should_hide_element($client_consumption_settings, $consumption_categories['volumen'], 'grafico'),
            "ocultar_tabla_consumos_masa" => $this->should_hide_element($client_consumption_settings, $consumption_categories['masa'], 'tabla'),
            "ocultar_grafico_consumos_masa" => $this->should_hide_element($client_consumption_settings, $consumption_categories['masa'], 'grafico'),
            "ocultar_tabla_consumos_energia" => $this->should_hide_element($client_consumption_settings, $consumption_categories['energia'], 'tabla'),
            "ocultar_grafico_consumos_energia" => $this->should_hide_element($client_consumption_settings, $consumption_categories['energia'], 'grafico'),
            "ocultar_tabla_residuos_volumen" => $this->should_hide_element($client_waste_settings, $waste_categories['volumen'], 'tabla'),
            "ocultar_grafico_residuos_volumen" => $this->should_hide_element($client_waste_settings, $waste_categories['volumen'], 'grafico'),
            "ocultar_grafico_residuos_masa" => $this->should_hide_element($client_waste_settings, $waste_categories['masa'], 'grafico'),
        ];
    }

    /**
     * Obtiene las categorías por tipo de unidad.
     *
     * @param array $forms Formularios.
     * @return array Categorías por tipo de unidad.
     */
    private function get_categories_by_unit_type(array $forms): array
    {
        $categories = [
            'masa' => [],
            'volumen' => [],
            'energia' => []
        ];

        foreach ($forms as $form) {
            $unit_data = json_decode($form->unidad, true);
            $unit_type_id = $unit_data["tipo_unidad_id"];
            $related_categories = $this->Form_rel_materiales_rel_categorias_model->get_all_where(["id_formulario" => $form->id])->result();

            foreach ($related_categories as $category) {
                if (in_array($unit_type_id, [1, 2, 4])) {
                    $categories[$unit_type_id == 1 ? 'masa' : ($unit_type_id == 2 ? 'volumen' : 'energia')][$category->id_categoria] = $category->id_categoria;
                }
            }
        }

        return $categories;
    }

    /**
     * Verifica si se debe ocultar un elemento (tabla o gráfico).
     *
     * @param array $settings Configuraciones del cliente.
     * @param array $categories Categorías.
     * @param string $element_type Tipo de elemento ('tabla' o 'grafico').
     * @return bool Verdadero si se debe ocultar, falso en caso contrario.
     */
    private function should_hide_element(array $settings, array $categories, string $element_type): bool
    {
        foreach ($settings as $setting) {
            if (in_array($setting["id_categoria"], $categories) && $setting[$element_type]) {
                return false;
            }
        }
        return true;
    }

    /**
     * Procesa los compromisos y permisos.
     *
     * @param int $id_proyecto ID del proyecto.
     * @param array $view_data Datos de la vista.
     * @return array Datos de la vista con datos de compromisos y permisos.
     */
    private function process_compromises_and_permits(int $id_proyecto, array $view_data): array
    {
        $client_id = $view_data['client_id'];

        $view_data = array_merge($view_data, $this->process_compromises($id_proyecto, $client_id));
        $view_data = array_merge($view_data, $this->process_permitting($id_proyecto, $client_id));

        return $view_data;
    }

    /**
     * Carga los datos de cálculo.
     *
     * @param int $id_proyecto ID del proyecto.
     * @param array $view_data Datos de la vista.
     * @return array Datos de la vista con datos de cálculo.
     */
    private function load_calculation_data(int $id_proyecto, array $view_data): array
    {
        $id_cliente = $view_data['client_id'];

        return [
            'Calculation_model' => $this->Calculation_model,
            'Fields_model' => $this->Fields_model,
            'Unity_model' => $this->Unity_model,
            'Forms_model' => $this->Forms_model,
            'Characterization_factors_model' => $this->Characterization_factors_model,
            'Form_rel_materiales_rel_categorias_model' => $this->Form_rel_materiales_rel_categorias_model,
            'Assignment_combinations_model' => $this->Assignment_combinations_model,
            'Module_footprint_units_model' => $this->Module_footprint_units_model,
            'Conversion_model' => $this->Conversion_model,
            'Tipo_tratamiento_model' => $this->Tipo_tratamiento_model,
            'array_factores' => $this->prepare_factores($id_proyecto),
            'array_transformaciones' => $this->prepare_transformaciones($id_proyecto),
            'calculos' => $this->Calculation_model->get_calculos($id_proyecto, $id_cliente, NULL, '2023-01-01', '2023-12-31')->result(),
            'sucursales' => $this->Subprojects_model->get_dropdown_list(["nombre"], "id", ["id_proyecto" => $id_proyecto, "deleted" => 0]),
            'sp_uf' => $this->Functional_units_model->get_dropdown_list(["id"], "id_subproyecto", ["id_proyecto" => $id_proyecto]),
            'campos_unidad' => $this->Fields_model->get_dropdown_list(["opciones"], "id", ["id_proyecto" => $id_proyecto, "id_tipo_campo" => 15]),
            'unidades' => $this->Unity_model->get_dropdown_list(["nombre"], 'id'),
            'tipo_tratamiento' => $this->Tipo_tratamiento_model->get_dropdown_list(["nombre"], "id", ["deleted" => 0]),
            'type_of_origin_matter' => $this->EC_Types_of_origin_matter_model->get_dropdown_list(["nombre"], 'id'),
            'type_of_origin' => $this->EC_Types_of_origin_model->get_dropdown_list(["nombre"], 'id'),
            'default_type' => $this->EC_Types_no_apply_model->get_dropdown_list(["nombre"], 'id'),
        ];
    }

    private function member_allowed(int $project_id): void
    {
        $is_member = $this->Project_members_model->get_all_where([
            "user_id" => $this->login_user->id,
            "project_id" => $project_id,
            "deleted" => 0
        ]);

        if ($is_member === 0) {
            redirect("forbidden");
        }
    }

    private function prepare_staff_dashboard(): array
    {
        $settings = [
            'timeline' => get_setting("module_timeline"),
            'attendance' => get_setting("module_attendance"),
            'event' => get_setting("module_event"),
            'invoice' => get_setting("module_invoice"),
            'expense' => get_setting("module_expense"),
            'ticket' => get_setting("module_ticket"),
            'project_timesheet' => get_setting("module_project_timesheet"),
        ];

        $access = [
            'expense' => $this->get_access_info("expense"),
            'invoice' => $this->get_access_info("invoice"),
            'ticket' => $this->get_access_info("ticket"),
            'timecards' => $this->get_access_info("attendance"),
        ];

        $view_data = [
            'show_timeline' => (bool)$settings['timeline'],
            'show_attendance' => (bool)$settings['attendance'],
            'show_event' => (bool)$settings['event'],
            'show_project_timesheet' => (bool)$settings['project_timesheet'],
            'show_invoice_statistics' => false,
            'show_ticket_status' => false,
            'show_income_vs_expenses' => false,
            'show_clock_status' => false,
        ];

        if ($settings['invoice'] && $settings['expense'] && $access['expense']->access_type === "all" && $access['invoice']->access_type === "all") {
            $view_data["show_income_vs_expenses"] = true;
        }

        if ($settings['invoice'] && $access['invoice']->access_type === "all") {
            $view_data["show_invoice_statistics"] = true;
        }

        if ($settings['ticket'] && $access['ticket']->access_type === "all") {
            $view_data["show_ticket_status"] = true;
        }

        if ($settings['attendance'] && $access['timecards']->access_type === "all") {
            $view_data["show_clock_status"] = true;
        }

        return $view_data;
    }

    private function redirect_client_dashboard(): void
    {
        $redirect = $this->session->project_context ? 'home' : 'inicio_projects';
        redirect($redirect);
    }

    private function get_report_unit(int $client_id, int $project_id, int $type_id): int
    {
        $setting = $this->Reports_units_settings_model->get_one_where([
            "id_cliente" => $client_id,
            "id_proyecto" => $project_id,
            "id_tipo_unidad" => $type_id,
            "deleted" => 0
        ]);

        return $setting->id_unidad ?? 0;
    }

    private function get_project_footprints(int $project_id, int $methodology_id = 1): array
    {
        $footprints = $this->Footprints_model->get_footprints_of_methodology($methodology_id)->result();
        $footprint_ids = array_column($footprints, 'id');

        return $this->Project_rel_footprints_model->get_footprints_of_project($project_id, ['footprint_ids' => $footprint_ids])->result();
    }

    private function prepare_factores(int $project_id): array
    {
        $factores = $this->Calculation_model->get_factores($project_id)->result();
        $array_factores = [];

        foreach ($factores as $factor) {
            $array_factores[$factor->id_bd][$factor->id_metodologia][$factor->id_huella][$factor->id_material][$factor->id_categoria][$factor->id_subcategoria][$factor->id_unidad] = (float)$factor->factor;
        }

        return $array_factores;
    }

    private function prepare_transformaciones(int $project_id): array
    {
        $transformaciones = $this->Calculation_model->get_transformaciones($project_id)->result();
        $array_transformaciones = [];

        foreach ($transformaciones as $transformacion) {
            $array_transformaciones[$transformacion->id] = (float)$transformacion->transformacion;
        }

        return $array_transformaciones;
    }

    private function prepare_consumption_charts(array $data): array
    {
        $valores_volumen = $this->calculo_valores_por_flujo_material($data['campos_unidad_consumo'], 2, 'Consumo', $data['id_unidad_volumen'], $data['years'], $data['meses']);
        $valores_masa = $this->calculo_valores_por_flujo_material($data['campos_unidad_consumo'], 1, 'Consumo', $data['id_unidad_masa'], $data['years'], $data['meses']);
        $valores_energia = $this->calculo_valores_por_flujo_material($data['campos_unidad_consumo'], 4, 'Consumo', $data['id_unidad_energia'], $data['years'], $data['meses']);

        $grafico_volumen = $this->generar_datos_grafico($valores_volumen, 'Consumo', $data['client_id'], $data['id_proyecto'], $data['years'], $data['meses']);
        $grafico_masa = $this->generar_datos_grafico($valores_masa, 'Consumo', $data['client_id'], $data['id_proyecto'], $data['years'], $data['meses']);
        $grafico_energia = $this->generar_datos_grafico($valores_energia, 'Consumo', $data['client_id'], $data['id_proyecto'], $data['years'], $data['meses']);

        return [
            'array_id_materiales_valores_volumen' => $valores_volumen,
            'array_grafico_consumos_volumen_data' => $grafico_volumen,
            'array_id_materiales_valores_masa' => $valores_masa,
            'array_grafico_consumos_masa_data' => $grafico_masa,
            'array_id_materiales_valores_energia' => $valores_energia,
            'array_grafico_consumos_energia_data' => $grafico_energia,
        ];
    }

    private function process_compromises(int $project_id, int $client_id): array
    {
        $compromiso_rca = $this->Compromises_rca_model->get_one_where([
            'id_proyecto' => $project_id,
            'deleted' => 0
        ]);

        $view_data = [];

        if ($compromiso_rca) {
            $evaluados = $this->Evaluated_rca_compromises_model->get_all_where([
                "id_compromiso" => $compromiso_rca->id,
                "deleted" => 0
            ])->result();

            $estados_cliente = $this->Compromises_compliance_status_model->get_details([
                "id_cliente" => $client_id,
                "tipo_evaluacion" => "rca",
            ])->result();

            $ultimas_evaluaciones = $this->Compromises_compliance_evaluation_rca_model->get_last_evaluations_of_project($project_id, NULL)->result();

            $array_estados = [];
            $total = 0;

            foreach ($estados_cliente as $estado) {
                if ($estado->categoria === "No Aplica") {
                    continue;
                }

                $array_estados[$estado->id] = [
                    "nombre_estado" => $estado->nombre_estado,
                    "categoria" => $estado->categoria,
                    "color" => $estado->color,
                    "evaluaciones" => [],
                    "cantidad_categoria" => 0,
                ];

                $cant = 0;
                foreach ($evaluados as $evaluado) {
                    foreach ($ultimas_evaluaciones as $ultima_evaluacion) {
                        if (
                            $ultima_evaluacion->id_estados_cumplimiento_compromisos === $estado->id &&
                            $ultima_evaluacion->id_evaluado === $evaluado->id
                        ) {
                            $array_estados[$estado->id]["evaluaciones"][] = $ultima_evaluacion;
                            $cant++;
                        }
                    }
                }

                $array_estados[$estado->id]["cantidad_categoria"] = $cant;
                $total += $cant;
            }

            $view_data["total_compromisos_aplicables"] = $total;
            $view_data["total_cantidades_estados_evaluados"] = $array_estados;

            $view_data["Client_compromises_settings_model"] = $this->Client_compromises_settings_model;
            $view_data["puede_ver_compromisos"] = $this->profile_access($this->session->user_id, 6, "ver", 3);
            $view_data["disponibilidad_modulo_compromisos"] = $this->Module_availability_model->get_one_where([
                "id_cliente" => $client_id,
                "id_proyecto" => $project_id,
                "id_modulo_cliente" => 6,
                "deleted" => 0
            ])->available;
        }
        return $view_data;
    }

    private function process_permitting(int $project_id, int $client_id): array
    {
        $permiso = $this->Permitting_model->get_one_where([
            'id_proyecto' => $project_id,
            'deleted' => 0
        ]);

        $view_data = [];

        if ($permiso) {
            $evaluados = $this->Evaluated_permitting_model->get_all_where([
                "id_permiso" => $permiso->id,
                "deleted" => 0
            ])->result();

            $estados_cliente = $this->Permitting_procedure_status_model->get_details([
                "id_cliente" => $client_id,
            ])->result();

            $ultimas_evaluaciones = $this->Permitting_procedure_evaluation_model->get_last_evaluations_of_project($project_id, NULL)->result();

            $array_estados = [];
            $total = 0;

            foreach ($estados_cliente as $estado) {
                if ($estado->categoria === "No Aplica") {
                    continue;
                }

                $array_estados[$estado->id] = [
                    "nombre_estado" => $estado->nombre_estado,
                    "categoria" => $estado->categoria,
                    "color" => $estado->color,
                    "evaluaciones" => [],
                    "cantidad_categoria" => 0,
                ];

                $cant = 0;
                foreach ($evaluados as $evaluado) {
                    foreach ($ultimas_evaluaciones as $ultima_evaluacion) {
                        if (
                            $ultima_evaluacion->id_estados_tramitacion_permisos === $estado->id &&
                            $ultima_evaluacion->id_evaluado === $evaluado->id
                        ) {
                            $array_estados[$estado->id]["evaluaciones"][] = $ultima_evaluacion;
                            $cant++;
                        }
                    }
                }

                $array_estados[$estado->id]["cantidad_categoria"] = $cant;
                $total += $cant;
            }

            $view_data["total_permisos_aplicables"] = $total;
            $view_data["total_cantidades_estados_evaluados_permisos"] = $array_estados;
            $view_data["Client_permitting_settings_model"] = $this->Client_permitting_settings_model;
            $view_data["puede_ver_permisos"] = $this->profile_access($this->session->user_id, 7, "ver", 5);
            $view_data["disponibilidad_modulo_permisos"] = $this->Module_availability_model->get_one_where([
                "id_cliente" => $client_id,
                "id_proyecto" => $project_id,
                "id_modulo_cliente" => 7,
                "deleted" => 0
            ])->available;
        }
        return $view_data;
    }

    private function prepare_drilldown_material(
        array $valores,
        int $id_material,
        int $year,
        string $flujo,
        int $client_id,
        int $project_id
    ): array {
        $categorias = $valores[$id_material] ?? [];
        $serie = [
            'id' => "id_drilldown_material_{$id_material}_{$year}",
            'name' => "{$this->Materials_model->get_one($id_material)->nombre} {$year}",
            'data' => []
        ];

        foreach ($categorias as $id_categoria => $datos) {
            $row_categoria = $flujo === 'Consumo'
                ? $this->Client_consumptions_settings_model->get_one_where([
                    'id_cliente' => $client_id,
                    'id_proyecto' => $project_id,
                    'id_categoria' => $id_categoria,
                    'deleted' => 0
                ])
                : $this->Client_waste_settings_model->get_one_where([
                    'id_cliente' => $client_id,
                    'id_proyecto' => $project_id,
                    'id_categoria' => $id_categoria,
                    'deleted' => 0
                ]);

            if ($row_categoria && $row_categoria->grafico) {
                $nombre_categoria = $this->get_categoria_nombre($id_categoria);
                $valor_categoria = array_sum($datos[$year] ?? []);

                if ($valor_categoria > 0) {
                    $serie['data'][] = [
                        'name' => $nombre_categoria,
                        'y' => $valor_categoria,
                        'id_categoria' => $id_categoria,
                    ];
                }
            }
        }

        $serie['year'] = $year;

        return $serie;
    }

    private function prepare_drilldown_categoria(
        array $valores,
        int $id_categoria,
        int $year,
        array $meses
    ): array {
        $serie = [
            'id' => "id_drilldown_categoria_{$id_categoria}_{$year}",
            'name' => "{$this->get_categoria_nombre($id_categoria)} {$year}",
            'data' => []
        ];

        foreach ($valores as $id_material => $categorias) {
            if (isset($categorias[$id_categoria])) {
                foreach ($categorias[$id_categoria][$year] ?? [] as $mes_num => $valor_mes) {
                    $serie['data'][] = [
                        'name' => $meses[$mes_num],
                        'y' => $valor_mes
                    ];
                }
            }
        }

        return $serie;
    }

    private function get_categoria_nombre(int $id_categoria): string
    {
        $alias = $this->Categories_alias_model->get_one_where([
            'id_categoria' => $id_categoria,
            'id_cliente' => $this->login_user->client_id,
            'deleted' => 0
        ]);

        if ($alias && !empty($alias->alias)) {
            return $alias->alias;
        }

        $categoria = $this->Categories_model->get_one_where([
            'id' => $id_categoria,
            'deleted' => 0
        ]);

        return $categoria->nombre ?? 'Sin Nombre';
    }

    private function calculo_valores_por_flujo_material(
        array $formularios,
        int $tipo_unidad,
        string $flujo,
        int $unidad_configuracion,
        array $years,
        array $meses
    ): array {
        $array_id_materiales_valores = [];

        foreach ($formularios as $formulario) {
            $datos_campo_unidad = json_decode($formulario->unidad, true);
            $id_tipo_unidad = $datos_campo_unidad["tipo_unidad_id"] ?? null;
            $id_unidad = $datos_campo_unidad["unidad_id"] ?? null;

            if ($id_tipo_unidad !== $tipo_unidad) {
                continue;
            }

            $materiales_rel_categorias = $this->Form_rel_materiales_rel_categorias_model->get_all_where([
                "id_formulario" => $formulario->id,
                "deleted" => 0
            ])->result();

            foreach ($materiales_rel_categorias as $mat_rel_cat) {
                foreach ($years as $year) {
                    foreach ($meses as $index => $mes) {
                        $array_id_materiales_valores[$mat_rel_cat->id_material][$mat_rel_cat->id_categoria][$year][$index] = 0;
                    }
                }

                $elementos_form = $this->Calculation_model->get_records_of_category_of_form(
                    $mat_rel_cat->id_categoria,
                    $mat_rel_cat->id_formulario,
                    $flujo
                )->result();

                foreach ($elementos_form as $elemento) {
                    $datos_decoded = json_decode($elemento->datos, true);
                    $fecha = $datos_decoded['fecha'] ?? null;
                    $valor = $datos_decoded["unidad_residuo"] ?? 0;

                    if (!$fecha) {
                        continue;
                    }

                    $fecha_timestamp = strtotime($fecha);
                    $agno = date('Y', $fecha_timestamp);
                    $mes_num = (int)date('n', $fecha_timestamp) - 1;

                    if ($id_unidad === $unidad_configuracion) {
                        $array_id_materiales_valores[$mat_rel_cat->id_material][$mat_rel_cat->id_categoria][$agno][$mes_num] += $valor;
                    } else {
                        $conversion = $this->Conversion_model->get_one_where([
                            "id_tipo_unidad" => $tipo_unidad,
                            "id_unidad_origen" => $id_unidad,
                            "id_unidad_destino" => $unidad_configuracion
                        ]);

                        if ($conversion) {
                            $valor_transformado = $valor * (float)$conversion->transformacion;
                            $array_id_materiales_valores[$mat_rel_cat->id_material][$mat_rel_cat->id_categoria][$agno][$mes_num] += $valor_transformado;
                        }
                    }
                }
            }
        }

        return $array_id_materiales_valores;
    }

    private function generar_datos_grafico(
        array $valores,
        string $flujo,
        int $client_id,
        int $project_id,
        array $years,
        array $meses
    ): array {
        $series = [];
        $drilldown_series = [];

        foreach ($years as $year) {
            $serie = [
                'name' => (string)$year,
                'data' => [],
            ];

            foreach ($valores as $id_material => $categorias) {
                $total_material = 0;

                foreach ($categorias as $id_categoria => $datos) {
                    $row_categoria = $flujo === 'Consumo'
                        ? $this->Client_consumptions_settings_model->get_one_where([
                            'id_cliente' => $client_id,
                            'id_proyecto' => $project_id,
                            'id_categoria' => $id_categoria,
                            'deleted' => 0
                        ])
                        : $this->Client_waste_settings_model->get_one_where([
                            'id_cliente' => $client_id,
                            'id_proyecto' => $project_id,
                            'id_categoria' => $id_categoria,
                            'deleted' => 0
                        ]);

                    if ($row_categoria && $row_categoria->grafico) {
                        $valor_categoria = array_sum($datos[$year] ?? []);
                        $total_material += $valor_categoria;
                    }
                }

                if ($total_material > 0) {
                    $nombre_material = $this->Materials_model->get_one($id_material)->nombre;
                    $serie['data'][] = [
                        'name' => $nombre_material,
                        'y' => $total_material,
                        'drilldown' => "id_drilldown_material_{$id_material}_{$year}"
                    ];

                    $drilldown_series[] = $this->prepare_drilldown_material($valores, $id_material, $year, $flujo, $client_id, $project_id);
                }
            }

            $series[] = $serie;
        }

        $drilldown_complete = [];
        foreach ($drilldown_series as $drilldown) {
            $serie = [
                'id' => $drilldown['id'],
                'name' => $drilldown['name'],
                'data' => []
            ];

            foreach ($drilldown['data'] as $categoria) {
                $serie['data'][] = [
                    'name' => $this->get_categoria_nombre($categoria['id_categoria']),
                    'y' => $categoria['y'],
                    'drilldown' => "id_drilldown_categoria_{$categoria['id_categoria']}_{$drilldown['year']}"
                ];
            }

            $drilldown_complete[] = $serie;

            foreach ($drilldown['data'] as $categoria) {
                $drilldown_complete[] = $this->prepare_drilldown_categoria($valores, $categoria['id_categoria'], $drilldown['year'], $meses);
            }
        }

        return [
            'series' => $series,
            'drilldown' => $drilldown_complete
        ];
    }

    private function prepare_waste_charts(array $data): array
    {
        $valores_volumen_residuo = $this->calculo_valores_por_flujo_material($data['campos_unidad_residuo'], 2, 'Residuo', $data['id_unidad_volumen'], $data['years'], $data['meses']);
        $valores_masa_residuo = $this->calculo_valores_por_flujo_material($data['campos_unidad_residuo'], 1, 'Residuo', $data['id_unidad_masa'], $data['years'], $data['meses']);

        $grafico_residuos_volumen = $this->generar_datos_grafico($valores_volumen_residuo, 'Residuo', $data['client_id'], $data['id_proyecto'], $data['years'], $data['meses']);
        $grafico_residuos_masa = $this->generar_datos_grafico($valores_masa_residuo, 'Residuo', $data['client_id'], $data['id_proyecto'], $data['years'], $data['meses']);

        return [
            'array_id_materiales_valores_volumen_residuo' => $valores_volumen_residuo,
            'array_grafico_residuos_volumen_data' => $grafico_residuos_volumen,
            'array_id_materiales_valores_masa_residuo' => $valores_masa_residuo,
            'array_grafico_residuos_masa_data' => $grafico_residuos_masa,
        ];
    }
}

/* End of file dashboard.php */
/* Location: ./application/controllers/dashboard.php */